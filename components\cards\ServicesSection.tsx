import ROUTES from "@/constants/routes";
import Image from "next/image";
import Link from "next/link";
import React from "react";

interface servicesObjType {
  title: string;
  body: string;
}

const servicesObjType = [
  {
    title: "E-commerce",
    body: "Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.",
  },
  {
    title: "Fast Moving Consumer Goods",
    body: "Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.",
  },
  {
    title: "Medical Goods",
    body: "Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.",
  },
  {
    title: "Automotive",
    body: "Reduce settlement time from days to seconds. Experience faster access to funds and improved cash flow.",
  },
];

const ServicesSection = () => {
  return (
    <div>
      <div className="flex my-[30px] sm:my-[40px] mb-[16px] sm:mb-[20px]">
        <Image
          src={"/icons/package-primary.svg"}
          alt="package"
          width={13}
          height={13.996332168579102}
        />
        <p className="text-small text-woodsmoke-800 font-semibold ml-[11.5px]">
          SERVICES
        </p>
      </div>
      <div className="flex flex-col md:flex-row items-start md:items-center gap-6 md:gap-4">
        <h3 className="h3-semibold w-full md:w-1/2">
          Our Innovative Solutions for Every Need.
        </h3>
        <p className="text-medium text-Woodsmoke-800 w-full md:w-1/2 pt-0 md:pt-4">
          Koolboks Logistics offers a range of services designed to meet diverse
          needs. Whether for homes, businesses, our services ensure reliable
          cooling without reliance on traditional energy sources.
        </p>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 mt-10">
        <div className="row-span-1 lg:row-span-2 bg-primary-500 w-full h-auto rounded-lg shadow py-[30px] sm:py-[40px] px-[20px] sm:px-[28px] flex flex-col">
          <div className="flex flex-1 flex-col justify-between h-full">
            <div>
              <h5 className="h5-semibold text-white mb-[16px] sm:mb-[24px]">
                The future of business is default global
              </h5>
              <p className="text-medium text-white mb-[20px]">
                The modern infrastructure to integrate stablecoins into global
                payment flows.
              </p>
            </div>
            <Link href={ROUTES.QUOTE} className="mt-6 sm:mt-0">
              <button type="button" className="btn-secondary">
                Request Quote
              </button>
            </Link>
          </div>
        </div>
        {servicesObjType.map((service) => (
          <div
            key={service.title}
            className="bg-white py-[30px] sm:py-[40px] px-[20px] sm:px-[28px] border-[0.92px] border-woodsmoke-100 rounded-[20px] flex flex-col"
          >
            <div className="bg-primary-50 h-[40px] w-[40px] rounded-[8px] flex items-center justify-center mb-[16px]">
              <Image
                src={"/icons/Headset.svg"}
                alt="package"
                width={19.500072479248047}
                height={21}
              />
            </div>
            <h4 className="font-semibold text-large mb-[14px] text-woodsmoke-950">
              {service.title}
            </h4>
            <p className="text-woodsmoke-950 text-regular">{service.body}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ServicesSection;
