'use client'

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import ROUTES from '@/constants/routes'

const Footer = () => {
  return (
    <footer className="bg-woodsmoke-950 w-full min-h-[340px] flex flex-col">
      {/* Main Footer Content */}
      <div className="flex-1 w-full flex items-center py-6 lg:py-8">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Mobile Layout */}
          <div className="lg:hidden flex flex-col gap-8 py-8">
            {/* Company Logo & Social */}
            <div className="flex flex-col gap-8">
              <div>
                <Image
                  src="/images/footer_logo.png"
                  alt="Kool Logistics Logo"
                  width={160}
                  height={50}
                  className="h-12 w-auto"
                />
              </div>
              <div className="flex gap-3">
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/x.svg"
                    alt="X (Twitter)"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/linkedin.svg"
                    alt="LinkedIn"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/instagram.svg"
                    alt="Instagram"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/facebook.svg"
                    alt="Facebook"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/youtube.svg"
                    alt="YouTube"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
              </div>
            </div>

            {/* Say Hello - Mobile */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white text-medium text-[18px] font-semibold">Say Hello</h3>
              <div className="flex flex-col text-[16px] gap-4 text-[#FFFFFF]">
                <p className="text-regular"><EMAIL></p>
                <p className="text-regular">****** 100 975 20 34</p>
              </div>
            </div>

            {/* Useful Link - Mobile */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white font-semibold text-[18px] text-medium">Useful Link</h3>
              <div className="flex flex-col gap-4 text-[16px] text-[#FFFFFF] ">
                <a href="#" className="text-regular ">About us</a>
                <a href="#" className="text-regular ">Pricing</a>
                <Link href={ROUTES.QUOTE} className="text-regular ">Quote</Link>
                <a href="#" className="text-regular ">Contact</a>
              </div>
            </div>

            {/* Our Services - Mobile */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white font-semibold text-[18px] text-medium">Our Services</h3>
              <div className="flex flex-col gap-4 text-[16px] text-[#FFFFFF]">
                <a href="#" className="text-regular ">Logistics</a>
                <a href="#" className="text-regular ">Manufacturing</a>
                <a href="#" className="text-regular ">Production</a>
                <a href="#" className="text-regular ">Automotive</a>
              </div>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden lg:flex items-start justify-between gap-8">
            {/* Company Logo & Social - Desktop */}
            <div className="flex flex-col gap-8 flex-shrink-0">
              <div>
                <Image
                  src="/images/footer_logo.png"
                  alt="Kool Logistics Logo"
                  width={156}
                  height={74}
                  className="h-12 w-auto"
                />
              </div>
              <div className="flex gap-3">
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/x.svg"
                    alt="X (Twitter)"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/linkedin.svg"
                    alt="LinkedIn"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/instagram.svg"
                    alt="Instagram"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/facebook.svg"
                    alt="Facebook"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
                <a href="#" className="w-9 h-9 bg-[#454545] rounded flex items-center justify-center hover:bg-[#F57D1C] transition-colors">
                  <Image
                    src="/icons/youtube.svg"
                    alt="YouTube"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                </a>
              </div>
            </div>

            {/* Say Hello - Desktop */}
            <div className="flex flex-col gap-6">
              <h3 className="text-medium text-[18px] text-white font-semibold">Say Hello</h3>
              <div className="flex flex-col gap-4">
                <p className="text-regular text-white text-[16px]"><EMAIL></p>
                <p className="text-regular text-white text-[16px]">****** 100 975 20 34</p>
              </div>
            </div>

            {/* Useful Link - Desktop */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white text-[18px] font-semibold text-medium">Useful Link</h3>
              <div className="flex flex-col gap-4">
                <a href="#" className="text-regular text-white text-[16px]">About us</a>
                <a href="#" className="text-regular text-white text-[16px]">Pricing</a>
                <Link href={ROUTES.QUOTE} className="text-regular text-white text-[16px]">Quote</Link>
                <a href="#" className="text-regular text-white text-[16px]">Contact</a>
              </div>
            </div>

            {/* Our Services - Desktop */}
            <div className="flex flex-col gap-6">
              <h3 className="text-white text-[18px] font-semibold text-medium">Our Services</h3>
              <div className="flex flex-col gap-4">
                <a href="#" className="text-regular text-white text-[16px]">Logistics</a>
                <a href="#" className="text-regular text-white text-[16px]">Manufacturing</a>
                <a href="#" className="text-regular text-white text-[16px]">Production</a>
                <a href="#" className="text-regular text-white text-[16px]">Automotive</a>
              </div>

            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="bg-[#F57D1C] w-full py-4 lg:py-0 lg:h-14">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 lg:gap-0 items-center text-center lg:text-left h-full">
            <p className="text-white font-regular text-[14px] text-small">
              Copyright © 2025 KOOL LOGISTICS. All Rights Reserved.
            </p>
            <div className="flex gap-8 text-white">
              <a href="#" className="font-regular text-[14px] text-small">Terms of Service</a>
              <a href="#" className="font-regular text-[14px] text-small">Privacy Policy</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer