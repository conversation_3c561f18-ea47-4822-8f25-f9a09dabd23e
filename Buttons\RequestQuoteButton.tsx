import React from 'react'
import Link from 'next/link'
import ROUTES from '@/constants/routes'

interface RequestQuoteButtonProps {
  href?: string
  onClick?: () => void
  className?: string
  children?: React.ReactNode
}

const RequestQuoteButton: React.FC<RequestQuoteButtonProps> = ({
  href = ROUTES.QUOTE,
  onClick,
  className = '',
  children = 'Request Quote'
}) => {
  const buttonClasses = `
    inline-flex items-center justify-center
    w-[157px] h-[48px]
    gap-[4px]
    pt-[12px] pr-[16px] pb-[12px] pl-[16px]
    bg-[#F57D1C] 
    text-white font-semibold text-[14px] leading-[1.2]
    rounded-[12px]
    border border-[#F57D1C] hover:border-[#E55A2B]
    transition-all duration-200
    whitespace-nowrap
    ${className}
  `.trim().replace(/\s+/g, ' ')

  if (onClick) {
    return (
      <button
        onClick={onClick}
        className={buttonClasses}
      >
        {children}
      </button>
    )
  }

  return (
    <Link
      href={href}
      className={buttonClasses}
    >
      {children}
    </Link>
  )
}

export default RequestQuoteButton