'use client'

import Image from 'next/image'

export default function Navbar() {
  return (
    <header className="flex items-center justify-end border-b border-woodsmoke-100 px-4 md:px-8 h-20 bg-white sticky top-0 z-30">
      <div className="flex items-center space-x-4 md:space-x-6">
        <div className="relative">
          <input
            type="text"
            aria-label="Search anything"
            placeholder="Search anything..."
            className="w-[275px] h-11 rounded-lg border border-greyscale-200 bg-white py-2 pl-10 pr-4 text-sm font-[500] placeholder:text-woodsmoke-600 text-woodsmoke-800 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          />
          <Image src="/icons/search.svg" alt="Search icon" width={20} height={20} className="absolute left-3 top-1/2 -translate-y-1/2 pointer-events-none" />
        </div>
        <button aria-label="Notifications" className="rounded-full focus:outline-none focus:ring-2 focus:ring-primary">
          <Image src="/icons/dashboard-bell.svg" alt="Notifications icon" width={40} height={40} />
        </button>
        <button className="flex items-center space-x-3 focus:outline-none focus:ring-2 focus:ring-primary rounded-full">
          <Image
            src="/icons/dashboard-avatar.svg"
            alt="User avatar"
            width={40}
            height={40}
            className="rounded-full object-cover"
          />
          <Image src="/icons/CaretDown.svg" alt="Dropdown icon" width={16} height={16} />
        </button>
      </div>
    </header>
  )
}
