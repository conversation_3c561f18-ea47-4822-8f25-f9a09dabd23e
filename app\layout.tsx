import type { Metada<PERSON> } from "next";
import { Manrope, Inter } from "next/font/google";
import "./globals.css";

const manrope = Manrope({
  variable: "--font-manrope",
});

const inter = Inter({ variable: "--font-inter" });

export const metadata: Metadata = {
  title: "Kool Logistics",
  description: "Generated by create next app",
};

export default function AppLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
      </head>
      <body
        className={`${manrope.variable} ${inter.variable} ${manrope.className} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
