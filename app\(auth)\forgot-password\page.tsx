'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

const ResetPassword = () => {
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState(['', '', '', '']);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showConfirmationAnimated, setShowConfirmationAnimated] = useState(false);

  useEffect(() => {
    if (showConfirmation) {
      // Start with opacity-0, then switch to opacity-100 for fade-in
      setShowConfirmationAnimated(false);
      const timer = setTimeout(() => setShowConfirmationAnimated(true), 10);
      return () => clearTimeout(timer);
    }
  }, [showConfirmation]);

  const nextStep = () => setStep((s) => Math.min(s + 1, 3));
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>, idx: number) => {
    const val = e.target.value;
    if (/^\d?$/.test(val)) { 
      const newOtp = [...otp];
      newOtp[idx] = val;
      setOtp(newOtp);
      if (val && idx < 3) {
        const nextInput = document.getElementById(`otp-${idx + 1}`) as HTMLInputElement | null;
        if (nextInput) nextInput.focus();
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      <div className="flex lg:hidden border-b border-woodsmoke-200 px-6 py-4 justify-center items-center">
        <Image
          alt="KOOL LOGISTICS logo"
          src="/icons/site-logo.svg"
          width={118}
          height={56}
          className="object-contain"
        />
      </div>
      {/* Left side */}
      <div className="lg:w-1/2 bg-primary-500 hidden lg:flex flex-col justify-between p-8 text-white">
        <Image src="/icons/login-logo.svg" alt="Logo" width={150} height={50} className="w-36 mb-10" />
        <div className="flex-center">
          <Image src="/icons/login-illustration.svg" alt="Illustration" width={450} height={300} className="h-auto" />
        </div>
        <div className="text-center mt-3">
          <h2 className="h5-semibold mb-3">Your Logistics Dashboard</h2>
          <p className="text-small text-woodsmoke-50 opacity-70">Everything you need easily to Order, Track, and Manage your Packages</p>
        </div>
      </div>

      {/* Right side */}
      <div className="lg:w-1/2 p-5 py-8 lg:p-10 lg:py-20 flex flex-col justify-start">
        <div className="max-w-[572px] w-full mx-auto">
          {step === 1 && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                nextStep();
              }}
            >
              <h1 className="h6-semibold lg:h5-semibold mb-3 text-center lg:text-left">Reset your password</h1>
              <p className="text-small font-normal text-woodsmoke-600 mb-8 text-center lg:text-left">
                Enter the email address associated with your account and we will send you a code to reset your password.
              </p>

              <label htmlFor="email" className="text-small font-semibold text-woodsmoke-950 mb-2 block">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                className="mb-6 w-full rounded-[12px] border border-woodsmoke-100 px-4 py-3 text-regular font-semibold text-woodsmoke-950 placeholder-woodsmoke-500 focus:ring-2 focus:ring-primary-400"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />

              <button
                type="submit"
                className="btn-primary w-full"
              >
                Continue
              </button>

              <div className="text-center mt-12">
                <a href="/sign-in" className="text-primary-500 text-small font-bold hover:underline">
                  Back to Sign In
                </a>
              </div>
            </form>
          )}

          {step === 2 && (
            <>
              <h3 className="h6-semibold lg:h5-semibold mb-3 text-center lg:text-left">Verification</h3>
              <p className="text-small font-normal text-woodsmoke-600 mb-8 text-center lg:text-left">Verify your email to continue</p>

              <div className="flex-center mb-10">
                <div className="bg-primary-50 rounded-full p-5 w-[80px] h-[80px] flex-center">
                  <Image src = "/icons/Email.svg" alt = "" width={40} height={40}/>
                </div>
              </div>

              <p className="text-center text-regular font-medium text-woodsmoke-500 mb-5">
                We’ve sent a verification code to your email
              </p>

              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  nextStep();
                }}
                className="flex-center space-x-4 mb-4"
              >
                {otp.map((digit, idx) => (
                  <input
                    key={idx}
                    id={`otp-${idx}`}
                    type="text"
                    maxLength={1}
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={digit}
                    onChange={(e) => handleOtpChange(e, idx)}
                    className="w-14 h-14 border border-woodsmoke-100 rounded-[12px] text-center text-regular font-normal focus:outline-none focus:ring-2 focus:ring-primary-400"
                    aria-label={`Verification code digit ${idx + 1}`}
                  />
                ))}
              </form>

              <p className="text-center text-small font-medium text-woodsmoke-500 mb-12">
                Didn’t receive the code?{' '}
                <button
                  type="button"
                  className="text-primary-500 font-bold hover:underline focus:outline-none"
                  onClick={() => alert('Code resent!')}
                >
                  Resend
                </button>
              </p>


              <div className="flex-between">
                <button
                  type="button"
                  onClick={prevStep}
                  className="btn-secondary w-[122px] hover:bg-woodsmoke-200"
                >
                  Back
                </button>
                <button
                  onClick={nextStep}
                  className="btn-primary w-[122px] hover:bg-primary-400"
                >
                  Continue
                </button>
              </div>
            </>
          )}

          {step === 3 && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                setShowConfirmation(true);
              }}
              className=""
            >
              <h1 className="h6-semibold lg:h5-semibold mb-3 text-center lg:text-left">Reset your password</h1>
              <p className="text-small font-normal text-woodsmoke-600 mb-8 text-center lg:text-left">
                Enter the new password to be associated with your account
              </p>

              <div className="mb-4 relative">
                <label htmlFor="password" className="block text-small font-semibold mb-2">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your new password"
                  className="w-full border border-woodsmoke-100 rounded-[12px] px-4 py-3 pr-12 text-woodsmoke-950 text-regular font-semibold placeholder-woodsmoke-500 focus:outline-none focus:ring-2 focus:ring-primary-400"
                />
                <span className="absolute right-4 top-[69%] -translate-y-1/2 cursor-pointer">
                  {/* You can add toggle password visibility here */}
                  <Image src = "/icons/eye-off.svg" alt = "" width={20} height={18}/>
                </span>
              </div>

              <div className="mb-7 relative">
                <label htmlFor="confirm-password" className="block text-small font-semibold mb-2">
                  Confirm Password
                </label>
                <input
                  type="password"
                  id="confirm-password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm your password"
                  className="w-full border border-woodsmoke-100 rounded-[12px] px-4 py-3 pr-12 text-woodsmoke-950 text-regular font-semibold placeholder-woodsmoke-500 focus:outline-none focus:ring-2 focus:ring-primary-400"
                />
                <span className="absolute right-4 top-[69%] -translate-y-1/2 cursor-pointer">
                  {/* You can add toggle password visibility here */}
                  <Image src = "/icons/eye-off.svg" alt = "" width={20} height={18}/>
                </span>
              </div>

              <button
                type="submit"
                className="btn-primary w-full"
              >
                Confirm
              </button>
            </form>
          )}
        </div>
      </div>

      {showConfirmation && (
        <div
          className={`fixed inset-0 z-50 bg-white flex-center transition-opacity duration-700 ease-in-out ${
            showConfirmationAnimated ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <main className="flex flex-col items-center space-y-6">
            <Image src="/icons/login-check.svg" alt="check" width={100} height={50} className="w-24 mb-8" />
            <h1 className="text-center h6-semibold lg:h5-semibold text-woodsmoke-950 leading-[140%] mb-3">
              Great Job 🎉<br />
              <span className="block">Your password was successfully changed</span>
            </h1>
            <p className="text-center text-small text-woodsmoke-600 leading-tight max-w-xs">
              Use the new password to log in to your account
            </p>
            <a
              href="/sign-in"
              className="mt-4 btn-primary w-[122px] text-center focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-1"
            >
              Continue
            </a>
          </main>
        </div>
      )}
    </div>
  );
};

export default ResetPassword;
