"use client";

import React, { useState } from "react";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ShippingServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onNext: () => void;
  onPrevious: () => void;
}

const services = [
  {
    label: "Standard",
    description: "5-7 business days",
    price: 8500,
    icon: <svg width="32" height="32" fill="none" viewBox="0 0 32 32"><rect width="32" height="32" rx="8" fill="#F57D1C" fillOpacity="0.08"/><path d="M8 22V12a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v10" stroke="#F57D1C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M24 22v-4a2 2 0 0 0-2-2h-2v6" stroke="#F57D1C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
  },
  {
    label: "Express",
    description: "2-3 business days",
    price: 15000,
    icon: <svg width="32" height="32" fill="none" viewBox="0 0 32 32"><rect width="32" height="32" rx="8" fill="#F57D1C" fillOpacity="0.08"/><path d="M8 22V12a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v10" stroke="#F57D1C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M24 22v-4a2 2 0 0 0-2-2h-2v6" stroke="#F57D1C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
  },
  {
    label: "Urgent",
    description: "Next business day",
    price: 35000,
    icon: <svg width="32" height="32" fill="none" viewBox="0 0 32 32"><rect width="32" height="32" rx="8" fill="#F57D1C" fillOpacity="0.08"/><path d="M16 10v6l4 2" stroke="#F57D1C" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><circle cx="16" cy="16" r="10" stroke="#F57D1C" strokeWidth="2"/></svg>
  }
];

const ShippingServiceModal: React.FC<ShippingServiceModalProps> = ({ isOpen, onClose, onNext, onPrevious }) => {
  const [selectedService, setSelectedService] = useState("Express");
  const [addInsurance, setAddInsurance] = useState(false);
  const [signatureRequired, setSignatureRequired] = useState(false);

  if (!isOpen) return null;

  return (
    <div
      style={{
        width: 930,
        height: 563,
        opacity: 1,
        top: 169,
        left: 255,
        borderRadius: 8,
        position: "absolute",
        background: "white",
        zIndex: 100,
      }}
      className="shadow-xl"
    >
      {/* Header */}
      <div className="flex items-center justify-between border-b border-greyscale-100 px-6 py-5" style={{ borderTopRightRadius: 8 }}>
        <h2 className="text-medium text-[18px] font-semibold text-woodsmoke-950">Create your shipment</h2>
        <button onClick={onClose} className="w-8 h-8 flex items-center justify-center text-greyscale-400 hover:text-woodsmoke-950 transition-colors">
          <X size={20} />
        </button>
      </div>

      {/* Progress Steps */}
      <div className="px-6 py-4 border-b border-greyscale-100">
        <div className="flex items-center space-x-8 overflow-x-auto">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-greyscale-200 rounded-full flex items-center justify-center">
              <span className="text-greyscale-500 text-sm font-medium">1</span>
            </div>
            <span className="text-greyscale-500 text-[12px] text-tiny font-medium">Shipment Details</span>
          </div>
          <div className="flex-1 h-px bg-greyscale-200"></div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-greyscale-200 rounded-full flex items-center justify-center">
              <span className="text-greyscale-500 text-sm font-medium">2</span>
            </div>
            <span className="text-greyscale-500 text-[12px] text-tiny font-medium">Package Details</span>
          </div>
          <div className="flex-1 h-px bg-greyscale-200"></div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 border border-primary-500 rounded-full flex items-center justify-center">
              <span className="text-primary-500 text-sm font-medium">3</span>
            </div>
            <span className="text-primary-500 text-[12px] text-tiny font-medium">Shipping Service</span>
          </div>
          <div className="flex-1 h-px bg-greyscale-200"></div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-greyscale-200 rounded-full flex items-center justify-center">
              <span className="text-greyscale-500 text-sm font-medium">4</span>
            </div>
            <span className="text-greyscale-500 text-sm font-medium">Confirmation</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-8">
        <h3 className="text-[16px] font-semibold text-woodsmoke-950 mb-6">Choose your service</h3>
        <div className="flex gap-6 mb-6">
          {services.map((service) => (
            <button
              key={service.label}
              onClick={() => setSelectedService(service.label)}
              className={`flex-1 flex flex-col items-center justify-center rounded-[12px] border transition-all px-6 py-8 ${selectedService === service.label ? 'border-primary-500 bg-primary-50' : 'border-greyscale-100 bg-greyscale-50'}`}
              style={{ minWidth: 0 }}
            >
              <div className="mb-3">{service.icon}</div>
              <div className="text-[16px] font-semibold text-woodsmoke-950 mb-1">{service.label}</div>
              <div className="text-[13px] text-woodsmoke-400 mb-2">{service.description}</div>
              <div className="text-[18px] font-bold text-woodsmoke-950">₦{service.price.toLocaleString()}</div>
            </button>
          ))}
        </div>
        <div className="flex flex-col gap-3 mb-8">
          <label className="flex items-center gap-2 cursor-pointer text-[14px] text-woodsmoke-700">
            <input type="checkbox" checked={addInsurance} onChange={() => setAddInsurance(!addInsurance)} />
            Add insurance - <span className="font-semibold">₦1,750</span>
            <span className="text-woodsmoke-400 text-[12px]">(This covers up to <span className="font-bold">₦75,000</span>)</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer text-[14px] text-woodsmoke-700">
            <input type="checkbox" checked={signatureRequired} onChange={() => setSignatureRequired(!signatureRequired)} />
            Signature Required - <span className="font-semibold">₦1,750</span>
            <span className="text-woodsmoke-400 text-[12px]">(This covers up to <span className="font-bold">₦75,000</span>)</span>
          </label>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end items-center gap-4 px-6 py-5 border-t border-greyscale-100 bg-white rounded-b-[8px]">
        <Button variant="outline" onClick={onPrevious} className="w-[164px] h-[44px] text-woodsmoke-700 border-greyscale-300">Previous</Button>
        <Button onClick={onNext} className="w-[164px] h-[44px] bg-primary-500 text-white font-medium rounded-[5px]">Next</Button>
      </div>
    </div>
  );
};

export default ShippingServiceModal;
