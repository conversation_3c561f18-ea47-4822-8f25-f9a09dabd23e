'use client'
import React from 'react'
import { notFound } from 'next/navigation'
import BlogPost from '@/components/BlogPost'
import { getBlogPostBySlug } from '../../../../data/blogPosts'

interface BlogDetailPageProps {
  params: Promise<{
    slug: string
  }>
}

const BlogDetailPage = ({ params }: BlogDetailPageProps) => {
  const { slug } = React.use(params)
  const blogData = getBlogPostBySlug(slug)

  if (!blogData) {
    notFound()
  }

  return (
    <BlogPost
      title={blogData.title}
      excerpt={blogData.excerpt}
      author={blogData.author}
      heroImage={blogData.heroImage}
      introduction={blogData.introduction}
      sections={blogData.sections}
    />
  )
}

export default BlogDetailPage
