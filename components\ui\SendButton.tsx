import React from 'react';

interface SendButtonProps {
  children?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

const SendButton: React.FC<SendButtonProps> = ({
  children = 'Send Message',
  onClick,
  disabled = false,
  type = 'submit',
  className = ''
}) => {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`w-full bg-[#F57D1C] hover:bg-primary-500 text-white px-6 md:px-8 py-3 md:py-4 rounded-[6px] md:rounded-[8px] font-semibold text-[14px] md:text-[16px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      {children}
    </button>
  );
};

export default SendButton;