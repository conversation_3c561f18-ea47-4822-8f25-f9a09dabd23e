"use client";

import Image from "next/image";

const navItems = [
  { baseIcon: "layout-dashboard", label: "Dashboard", href: "/dashboard" },
  { baseIcon: "manage-orders", label: "Manage Orders", href: "/dashboard/orders" },
  { baseIcon: "track-shipment", label: "Track Shipment", href: "/dashboard/shipments" },
  { baseIcon: "invoices", label: "Invoices", href: "/dashboard/invoices" },
  { baseIcon: "settings", label: "Settings", href: "/dashboard/settings" },
];

import { usePathname } from "next/navigation";

export default function Sidebar() {
  const pathname = usePathname();
  return (
    <nav className="hidden md:flex flex-col w-64 bg-white border-r border-woodsmoke-100">
      <div className="px-8 h-20 flex items-center gap-3 mb-10 border-b border-woodsmoke-100">
        <Image src="/icons/dashboard-menu.svg" alt="Kool Logistics logo" width={24} height={24} />
        <Image src="/icons/dashboard-logo.svg" alt="Kool Logistics logo" width={93} height={44} />
      </div>
      <ul className="space-y-2 text-woodsmoke-700 text-sm font-medium px-4">
        {navItems.map(({ baseIcon, label, href }) => {
          const isActive = pathname === href;
          const iconPath = isActive
            ? `/icons/${baseIcon}-active.svg`
            : `/icons/${baseIcon}.svg`;

          return (
            <li key={label}>
              <a
                href={href}
                className={`flex items-center space-x-3 ${
                  isActive
                    ? "text-primary-500 bg-primary-50 rounded-xl transition-colors text-small font-[500] py-3 px-4"
                    : "hover:bg-woodsmoke-50 transition-colors rounded-xl text-woodsmoke-600 hover:text-woodsmoke-800 text-small font-[500] py-3 px-4"
                }`}
                aria-current={isActive ? "page" : undefined}
              >
                <Image
                  src={iconPath}
                  alt={`i`}
                  width={22}
                  height={22}
                  
                />
                <span>{label}</span>
              </a>
            </li>
          );
        })}
      </ul>
    </nav>
  );
}
