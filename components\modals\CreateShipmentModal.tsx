"use client";
import React, { useState } from 'react';
import { X, Calendar as CalendarIcon, ChevronDownIcon, Truck, Zap, AlertTriangle} from 'lucide-react';
import Map from '@/components/Map';
import Image from 'next/image';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface CreateShipmentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateShipmentModal: React.FC<CreateShipmentModalProps> = ({ isOpen, onClose }) => {
  const [pickupAddress, setPickupAddress] = useState('');
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [pickupDate, setPickupDate] = useState<Date | undefined>(undefined);
  const [preferredTime, setPreferredTime] = useState('');
  const [senderNumber, setSenderNumber] = useState('');
  const [receiverNumber, setReceiverNumber] = useState('');
  const [currentStep, setCurrentStep] = useState(1);

  // Package Details State
  const [packageCategory, setPackageCategory] = useState('');
  const [item, setItem] = useState('');
  const [packageWeight, setPackageWeight] = useState('');
  const [packageValue, setPackageValue] = useState('');
  const [quantity, setQuantity] = useState('');
  const [length, setLength] = useState('');
  const [width, setWidth] = useState('');
  const [height, setHeight] = useState('');
  const [packageDescription, setPackageDescription] = useState('');

  // Shipping Service State
  const [selectedService, setSelectedService] = useState('express');
  const [addInsurance, setAddInsurance] = useState(false);
  const [signatureRequired, setSignatureRequired] = useState(false);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      <div
        className="bg-white rounded-[8px] relative overflow-hidden shadow-xl pointer-events-auto w-full max-w-[95vw] lg:max-w-[930px] flex flex-col mb-3"
        style={{
          height: '90vh',
          maxHeight: '900px',
          opacity: 1,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          position: 'absolute',
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05)'
        }}
      >
        {/* Header */}
        <div
          className="flex items-center justify-between border-b border-greyscale-100"
          style={{
            width: '930px',
            height: '74px',
            opacity: 1,
            gap: '20px',
            borderBottomWidth: '1px',
            paddingTop: '20px',
            paddingRight: '24px',
            paddingBottom: '20px',
            paddingLeft: '24px',
            borderTopRightRadius: '8px'
          }}
        >
          <h2 className="text-medium text-[18px] font-semibold text-woodsmoke-950">Create your shipment</h2>
          <button
            onClick={onClose}
            className="w-6 h-6 flex items-center justify-center text-woodsmoke-600 hover:text-woodsmoke-800"
          >
            <X size={20} />
          </button>
        </div>

        {/* Progress Steps - Hide on success step */}
        {currentStep < 5 && (
          <div className="px-6 py-4 border-b border-greyscale-100">
          <div className="flex items-center space-x-8">
            {/* Step 1 */}
            <div className="flex items-center space-x-2 cursor-pointer" onClick={() => setCurrentStep(1)}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === 1 ? 'bg-primary-500' : 'bg-greyscale-200'
              }`}>
                <span className={`text-sm font-medium ${
                  currentStep === 1 ? 'text-white' : 'text-greyscale-500'
                }`}>1</span>
              </div>
              <span className={`text-[12px] text-tiny font-medium ${
                currentStep === 1 ? 'text-primary-500' : 'text-greyscale-500'
              }`}>Shipment Details</span>
            </div>

            {/* Connector */}
            <div className="flex-1 h-px bg-greyscale-200"></div>

            {/* Step 2 */}
            <div className="flex items-center space-x-2 cursor-pointer" onClick={() => setCurrentStep(2)}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === 2 ? 'bg-primary-500' : 'bg-greyscale-200'
              }`}>
                <span className={`text-sm font-medium ${
                  currentStep === 2 ? 'text-white' : 'text-greyscale-500'
                }`}>2</span>
              </div>
              <span className={`text-[12px] text-tiny font-medium ${
                currentStep === 2 ? 'text-primary-500' : 'text-greyscale-500'
              }`}>Package Details</span>
            </div>

            {/* Connector */}
            <div className="flex-1 h-px bg-greyscale-200"></div>

            {/* Step 3 */}
            <div className="flex items-center space-x-2 cursor-pointer" onClick={() => setCurrentStep(3)}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === 3 ? 'bg-primary-500' : 'bg-greyscale-200'
              }`}>
                <span className={`text-sm font-medium ${
                  currentStep === 3 ? 'text-white' : 'text-greyscale-500'
                }`}>3</span>
              </div>
              <span className={`text-[12px] text-tiny font-medium ${
                currentStep === 3 ? 'text-primary-500' : 'text-greyscale-500'
              }`}>Shipping Service</span>
            </div>

            {/* Connector */}
            <div className="flex-1 h-px bg-greyscale-200"></div>

            {/* Step 4 */}
            <div className="flex items-center space-x-2 cursor-pointer" onClick={() => setCurrentStep(4)}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                currentStep === 4 ? 'bg-primary-500' : 'bg-greyscale-200'
              }`}>
                <span className={`text-sm font-medium ${
                  currentStep === 4 ? 'text-white' : 'text-greyscale-500'
                }`}>4</span>
              </div>
              <span className={`text-[12px] text-tiny font-medium ${
                currentStep === 4 ? 'text-primary-500' : 'text-greyscale-500'
              }`}>Confirmation</span>
            </div>
          </div>
        </div>
        )}

        {/* Content */}
        <div
          className="flex-1 border-b border-greyscale-100 overflow-y-auto"
          style={{
            width: '930px',
            height: '598px',
            opacity: 1,
            gap: '16px',
            borderBottomWidth: '1px',
            paddingTop: '20px',
            paddingRight: '24px',
            paddingBottom: '20px',
            paddingLeft: '24px'
          }}
        >
          {/* Step 1: Shipment Details */}
          {currentStep === 1 && (
            <>
          {/* Address Section */}
          <div className="mb-6">
            <div className="flex items-center space-x-4 mb-4">
              <Image
                src="/icons/PaperPlaneTilt.svg"
                alt="Paper Plane"
                width={20}
                height={20}
                className="text-greyscale-600"
              />
              <div className="flex-1 flex items-center space-x-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Pickup Address"
                    value={pickupAddress}
                    onChange={(e) => setPickupAddress(e.target.value)}
                    className="border border-greyscale-200 text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    style={{
                      width: '337px',
                      height: '44px',
                      opacity: 1,
                      borderRadius: '12px',
                      gap: '12px',
                      borderWidth: '1px',
                      paddingTop: '11px',
                      paddingRight: '40px',
                      paddingBottom: '11px',
                      paddingLeft: '16px'
                    }}
                  />
                  {pickupAddress && (
                    <X
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-greyscale-500 cursor-pointer hover:text-greyscale-700"
                      onClick={() => setPickupAddress('')}
                    />
                  )}
                </div>

                {/* Line Connector */}
                <div className="flex items-center justify-center px-4">
                  <Image
                    src="/images/Line.png"
                    alt="Line connector"
                    width={64}
                    height={2}
                  />
                </div>

                <Image
                  src="/icons/MapPin.svg"
                  alt="Map Pin"
                  width={20}
                  height={20}
                  className="text-greyscale-600"
                />
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Delivery Address"
                    value={deliveryAddress}
                    onChange={(e) => setDeliveryAddress(e.target.value)}
                    className="border border-greyscale-200 text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    style={{
                      width: '337px',
                      height: '44px',
                      opacity: 1,
                      borderRadius: '12px',
                      gap: '12px',
                      borderWidth: '1px',
                      paddingTop: '11px',
                      paddingRight: '40px',
                      paddingBottom: '11px',
                      paddingLeft: '16px'
                    }}
                  />
                  {deliveryAddress && (
                    <X
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-greyscale-500 cursor-pointer hover:text-greyscale-700"
                      onClick={() => setDeliveryAddress('')}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Map Section */}
          <div className="mb-6">
            <div className="w-full h-[307px] bg-greyscale-100 rounded-[12px] relative overflow-hidden">
              <Map />
              {/* Zoom Controls */}
              <div className="absolute bottom-4 right-4 flex flex-col space-y-2 z-10">
                <button className="w-10 h-10 bg-white rounded-[8px] shadow-sm flex items-center justify-center text-woodsmoke-800 hover:bg-greyscale-50">
                  +
                </button>
                <button className="w-10 h-10 bg-white rounded-[8px] shadow-sm flex items-center justify-center text-woodsmoke-800 hover:bg-greyscale-50">
                  −
                </button>
              </div>

            </div>
          </div>

          {/* Form Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6 md:mb-8">
            {/* Pickup Date */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">Pickup Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between text-left font-normal px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm hover:bg-transparent focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <span>{pickupDate ? pickupDate.toLocaleDateString() : "Select date"}</span>
                    <CalendarIcon className="h-4 w-4 text-greyscale-500" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={pickupDate}
                    onSelect={setPickupDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Preferred Time */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">Preferred Time</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between text-left font-normal px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm hover:bg-transparent focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <span className="text-greyscale-500">
                      {preferredTime === 'morning' && 'Morning (8AM - 12PM)'}
                      {preferredTime === 'afternoon' && 'Afternoon (12PM - 5PM)'}
                      {preferredTime === 'evening' && 'Evening (5PM - 8PM)'}
                      {!preferredTime && 'Select your preferred time'}
                    </span>
                    <ChevronDownIcon className="h-4 w-4 text-greyscale-500" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <div className="flex flex-col">
                    <button
                      onClick={() => setPreferredTime('morning')}
                      className="px-4 py-3 text-left text-sm hover:bg-greyscale-50 focus:bg-greyscale-50 focus:outline-none"
                    >
                      Morning (8AM - 12PM)
                    </button>
                    <button
                      onClick={() => setPreferredTime('afternoon')}
                      className="px-4 py-3 text-left text-sm hover:bg-greyscale-50 focus:bg-greyscale-50 focus:outline-none"
                    >
                      Afternoon (12PM - 5PM)
                    </button>
                    <button
                      onClick={() => setPreferredTime('evening')}
                      className="px-4 py-3 text-left text-sm hover:bg-greyscale-50 focus:bg-greyscale-50 focus:outline-none"
                    >
                      Evening (5PM - 8PM)
                    </button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Sender Number */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">Sender Number</label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Sender Number"
                  value={senderNumber}
                  onChange={(e) => setSenderNumber(e.target.value)}
                  className="w-full px-4 py-3 pr-10 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                {senderNumber && (
                  <X
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-greyscale-500 cursor-pointer hover:text-greyscale-700"
                    onClick={() => setSenderNumber('')}
                  />
                )}
              </div>
            </div>

            {/* Receiver Number */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">Receiver Number</label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Receiver Number"
                  value={receiverNumber}
                  onChange={(e) => setReceiverNumber(e.target.value)}
                  className="w-full px-4 py-3 pr-10 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                {receiverNumber && (
                  <X
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-greyscale-500 cursor-pointer hover:text-greyscale-700"
                    onClick={() => setReceiverNumber('')}
                  />
                )}
              </div>
            </div>
          </div>
            </>
          )}

          {/* Step 2: Package Details */}
          {currentStep === 2 && (
            <>
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-woodsmoke-950 mb-6">Package Details</h3>

                {/* Package Category and Item */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                      Package Category <span style={{ color: '#DF1C41' }}>*</span>
                    </label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-between text-left font-normal h-12 px-4 border-greyscale-200 hover:border-greyscale-200"
                        >
                          {packageCategory || "Select category"}
                          <ChevronDownIcon className="h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <div className="max-h-60 overflow-y-auto">
                          {['Electronics', 'Clothing', 'Books', 'Food', 'Furniture', 'Other'].map((category) => (
                            <div
                              key={category}
                              className="px-4 py-2 hover:bg-greyscale-50 cursor-pointer"
                              onClick={() => setPackageCategory(category)}
                            >
                              {category}
                            </div>
                          ))}
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div>
                    <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                      Item <span style={{ color: '#DF1C41' }}>*</span>
                    </label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-between text-left font-normal h-12 px-4 border-greyscale-200 hover:border-greyscale-200"
                        >
                          {item || "Select item"}
                          <ChevronDownIcon className="h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <div className="max-h-60 overflow-y-auto">
                          {['Laptop', 'Phone', 'Tablet', 'Headphones', 'Camera', 'Other'].map((itemOption) => (
                            <div
                              key={itemOption}
                              className="px-4 py-2 hover:bg-greyscale-50 cursor-pointer"
                              onClick={() => setItem(itemOption)}
                            >
                              {itemOption}
                            </div>
                          ))}
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Weight, Value, Quantity */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  <div>
                    <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                     Package Weight (KG) <span style={{ color: '#DF1C41' }}>*</span>
                    </label>
                    <input
                      type="text"
                      placeholder="0.0"
                      value={packageWeight}
                      onChange={(e) => setPackageWeight(e.target.value)}
                      className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                      Value (Naira) <span style={{ color: '#DF1C41' }}>*</span>
                    </label>
                    <input
                      type="text"
                      placeholder="0.00"
                      value={packageValue}
                      onChange={(e) => setPackageValue(e.target.value)}
                      className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                      Quantity <span style={{ color: '#DF1C41' }}>*</span>
                    </label>
                    <input
                      type="text"
                      placeholder="1"
                      value={quantity}
                      onChange={(e) => setQuantity(e.target.value)}
                      className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Dimensions */}
                <div className="mb-6">
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                        Length (Optional)
                      </label>
                      <input
                        type="text"
                        placeholder="Length (cm)"
                        value={length}
                        onChange={(e) => setLength(e.target.value)}
                        className="px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                        Width (Optional)
                      </label>
                      <input
                        type="text"
                        placeholder="Width (cm)"
                        value={width}
                        onChange={(e) => setWidth(e.target.value)}
                        className="px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                        Height (Optional)
                      </label>
                      <input
                        type="text"
                        placeholder="Height (cm)"
                        value={height}
                        onChange={(e) => setHeight(e.target.value)}
                        className="px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                    Package Description (Optional)
                  </label>
                  <textarea
                    placeholder="Describe the contents of your package..."
                    value={packageDescription}
                    onChange={(e) => setPackageDescription(e.target.value)}
                    rows={4}
                    className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                  />
                </div>
              </div>
            </>
          )}

          {/* Step 3: Shipping Service */}
          {currentStep === 3 && (
            <>
              <div className="mb-8">
                <h3 className="text-small font-semibold text-[14px] text-woodsmoke-950 mb-6">Choose your service</h3>

                {/* Service Options */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  {/* Standard */}
                  <div
                    className={`cursor-pointer transition-all ${
                      selectedService === 'standard'
                        ? 'bg-primary-50'
                        : 'bg-woodsmoke-50'
                    }`}
                    onClick={() => setSelectedService('standard')}
                    style={{
                      width: '278px',
                      height: '156px',
                      opacity: 1,
                      borderRadius: '12px',
                      gap: '16px',
                      padding: '20px'
                    }}
                  >
                    <div className="flex flex-col items-center text-center">
                      <Truck className="h-8 w-8 text-primary-500 mb-4" />
                      <h4 className="text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">Standard</h4>
                      <p className="text-tiny text-[12px] text-woodsmoke-500 mb-4">5-7 business days</p>
                      <p className="text-small text-[14px] font-semibold text-woodsmoke-950">₦8,500</p>
                    </div>
                  </div>

                  {/* Express - Selected by default */}
                  <div
                    className={`cursor-pointer transition-all ${
                      selectedService === 'express'
                        ? 'bg-primary-50'
                        : 'bg-woodsmoke-50'
                    }`}
                    onClick={() => setSelectedService('express')}
                    style={{
                      width: '278px',
                      height: '156px',
                      opacity: 1,
                      borderRadius: '12px',
                      gap: '16px',
                      padding: '20px'
                    }}
                  >
                    <div className="flex flex-col items-center text-center">
                      <Zap className="h-8 w-8 text-primary-500 mb-4" />
                      <h4 className="text-small text-[14px] font-semibold text-woodsmoke-950 mb-2">Express</h4>
                      <p className="text-tiny text-[12px] text-woodsmoke-500 mb-4">2-3 business days</p>
                      <p className="text-small text-[14px] font-semibold text-woodsmoke-950">₦15,000</p>
                    </div>
                  </div>

                  {/* Urgent */}
                  <div
                    className={`border rounded-lg p-6 cursor-pointer transition-all ${
                      selectedService === 'urgent'
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-greyscale-200 bg-greyscale-50 hover:border-greyscale-200'
                    }`}
                    onClick={() => setSelectedService('urgent')}
                  >
                    <div className="flex flex-col items-center text-center">
                      <AlertTriangle className="h-8 w-8 text-primary-500 mb-4" />
                      <h4 className="text-small text-[14px] font-semibold text-woodsmoke-950 mb-2">Urgent</h4>
                      <p className="text-tiny text-[12px] text-woodsmoke-500 mb-4">Next business day</p>
                      <p className="text-small text-[14px] font-semibold text-woodsmoke-950">₦35,000</p>
                    </div>
                  </div>
                </div>

                {/* Additional Services */}
                <div className="space-y-4">
                  {/* Add Insurance */}
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id="insurance"
                      checked={addInsurance}
                      onChange={(e) => setAddInsurance(e.target.checked)}
                      className="mt-1 h-4 w-4 text-primary-500 border-greyscale-200 rounded focus:ring-primary-500"
                    />
                    <div className="flex-1">
                      <label htmlFor="insurance" className="text-sm font-medium text-woodsmoke-950 cursor-pointer">
                        Add insurance - <span className="font-bold">₦1,750</span>
                      </label>
                      <p className="text-xs text-greyscale-600 mt-1">
                        (This covers up <span className="font-semibold">₦75,000</span>)
                      </p>
                    </div>
                  </div>

                  {/* Signature Required */}
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id="signature"
                      checked={signatureRequired}
                      onChange={(e) => setSignatureRequired(e.target.checked)}
                      className="mt-1 h-4 w-4 text-primary-500 border-greyscale-200 rounded focus:ring-primary-500"
                    />
                    <div className="flex-1">
                      <label htmlFor="signature" className="text-sm font-medium text-woodsmoke-950 cursor-pointer">
                        Signature Required - <span className="font-bold">₦1,750</span>
                      </label>
                      <p className="text-xs text-greyscale-600 mt-1">
                        (This covers up <span className="font-semibold">₦75,000</span>)
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Step 4: Confirmation */}
          {currentStep === 4 && (
            <>
              {/* Order Summary */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-woodsmoke-950 mb-6">Order Summary</h3>

                <div className="space-y-4 mb-8">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-greyscale-600">Pickup</span>
                    <span className="text-sm text-woodsmoke-950 font-medium">{pickupAddress || 'Acme road, Ikeja'}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-greyscale-600">Destination</span>
                    <span className="text-sm text-woodsmoke-950 font-medium">{deliveryAddress || 'Lakwe, Ajah, Lekki'}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-greyscale-600">Service</span>
                    <span className="text-sm text-woodsmoke-950 font-medium">
                      {selectedService === 'standard' && 'Standard (5-7 days)'}
                      {selectedService === 'express' && 'Express (2-3 days)'}
                      {selectedService === 'urgent' && 'Urgent (Next day)'}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-greyscale-600">Package</span>
                    <span className="text-sm text-woodsmoke-950 font-medium">
                      {packageCategory || 'Small Package'} ({packageWeight || '2.5'}kg)
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-greyscale-600">Shipping Fee</span>
                    <span className="text-sm text-woodsmoke-950 font-medium">
                      ₦{selectedService === 'standard' ? '8,500' : selectedService === 'express' ? '15,000' : '35,000'}
                    </span>
                  </div>

                  {addInsurance && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-greyscale-600">Insurance</span>
                      <span className="text-sm text-woodsmoke-950 font-medium">₦1,750</span>
                    </div>
                  )}

                  {signatureRequired && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-greyscale-600">Signature Required</span>
                      <span className="text-sm text-woodsmoke-950 font-medium">₦1,750</span>
                    </div>
                  )}

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-greyscale-600">Tax</span>
                    <span className="text-sm text-woodsmoke-950 font-medium">₦7,100</span>
                  </div>

                  <div className="border-t border-greyscale-200 pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-base font-semibold text-woodsmoke-950">TOTAL</span>
                      <span className="text-base font-bold text-woodsmoke-950">₦40,000</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Method */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-woodsmoke-950 mb-6">Payment Method</h3>

                {/* Payment Options */}
                <div className="flex space-x-6 mb-6">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="card"
                      defaultChecked
                      className="w-4 h-4 text-primary-500 border-greyscale-200 focus:ring-primary-500"
                    />
                    <span className="text-sm font-medium text-woodsmoke-950">Debit/Credit card</span>
                  </label>

                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="stripe"
                      className="w-4 h-4 text-primary-500 border-greyscale-200 focus:ring-primary-500"
                    />
                    <span className="text-sm font-medium text-woodsmoke-950">Stripe</span>
                  </label>

                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="selar"
                      className="w-4 h-4 text-primary-500 border-greyscale-200 focus:ring-primary-500"
                    />
                    <span className="text-sm font-medium text-woodsmoke-950">Selar</span>
                  </label>
                </div>

                {/* Card Details Form */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-woodsmoke-950 mb-2">
                      Cardholder Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      placeholder="Cardholder Name"
                      className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-woodsmoke-950 mb-2">
                      Card Number <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      placeholder="Card Number"
                      className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-woodsmoke-950 mb-2">
                      Expiry Date
                    </label>
                    <input
                      type="text"
                      placeholder="MM/YY"
                      className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-woodsmoke-950 mb-2">
                      CVV
                    </label>
                    <input
                      type="text"
                      placeholder="CVV"
                      className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Step 5: Success */}
          {currentStep === 5 && (
            <>
              <div className="flex flex-col items-center justify-center py-12 text-center">
                {/* Success Icon */}
                <div className="relative mb-8">
                  {/* Background decorative elements */}
                  <div className="absolute -top-2 -left-2 w-3 h-3 bg-green-400 rounded-full"></div>
                  <div className="absolute -top-1 -right-3 w-2 h-2 bg-orange-400 rounded-full"></div>
                  <div className="absolute -bottom-2 -left-3 w-2 h-2 bg-blue-400 rounded-full"></div>
                  <div className="absolute -bottom-1 -right-2 w-3 h-3 bg-yellow-400 rounded-full"></div>

                  {/* Main icon container */}
                  <div className="relative">
                    {/* Blue background circle */}
                    <div className="w-20 h-20 bg-blue-500 rounded-2xl flex items-center justify-center mb-2">
                      <div className="w-12 h-8 bg-blue-600 rounded-sm"></div>
                    </div>

                    {/* Green checkmark circle */}
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Success Message */}
                <h3 className="text-xl font-semibold text-woodsmoke-950 mb-2">
                  Success 🎉
                </h3>
                <p className="text-greyscale-600 mb-8">
                  Your shipment was successfully booked!
                </p>

                {/* Tracking Number */}
                <div className="bg-greyscale-50 rounded-lg p-4 mb-8 w-full max-w-md">
                  <p className="text-sm text-greyscale-600 mb-1">Your Tracking Number</p>
                  <p className="text-lg font-bold text-woodsmoke-950">0023GHD425</p>
                </div>

                {/* Order Details */}
                <div className="w-full max-w-md space-y-4 mb-8">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-greyscale-600">Order ID</span>
                    <span className="text-sm font-medium text-woodsmoke-950">SHIP12345</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-greyscale-600">Estimated Delivery</span>
                    <span className="text-sm font-medium text-woodsmoke-950">Apr 12, 2025</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-greyscale-600">Pickup Scheduled</span>
                    <span className="text-sm font-medium text-woodsmoke-950">Apr 10, 2025 (Morning)</span>
                  </div>

                  <div className="flex justify-between items-center pt-4 border-t border-greyscale-200">
                    <span className="text-base font-semibold text-woodsmoke-950">TOTAL</span>
                    <span className="text-base font-bold text-woodsmoke-950">₦40,000</span>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-greyscale-100 flex justify-end" style={{ gap: '4px' }}>
          {currentStep > 1 && currentStep < 5 && (
            <button
              onClick={() => setCurrentStep(currentStep - 1)}
              className="border border-greyscale-200 text-sm font-medium text-woodsmoke-700 hover:bg-greyscale-50 transition-colors"
              style={{
                width: '164px',
                height: '44px',
                opacity: 1,
                borderRadius: '5px',
                paddingTop: '12px',
                paddingRight: '10px',
                paddingBottom: '12px',
                paddingLeft: '10px',
                borderWidth: '1px'
              }}
            >
              Previous
            </button>
          )}

          {currentStep < 4 ? (
            <>
              {currentStep === 1 && (
                <button
                  onClick={onClose}
                  className="border border-greyscale-200 text-sm font-medium text-woodsmoke-700 hover:bg-greyscale-50 transition-colors"
                  style={{
                    width: '164px',
                    height: '44px',
                    opacity: 1,
                    borderRadius: '5px',
                    paddingTop: '12px',
                    paddingRight: '10px',
                    paddingBottom: '12px',
                    paddingLeft: '10px',
                    borderWidth: '1px'
                  }}
                >
                  Cancel
                </button>
              )}
              <button
                onClick={() => setCurrentStep(currentStep + 1)}
                className="bg-primary-500 text-sm font-medium text-white hover:bg-primary-400 transition-colors border-0"
                style={{
                  width: '164px',
                  height: '44px',
                  opacity: 1,
                  borderRadius: '5px',
                  paddingTop: '12px',
                  paddingRight: '10px',
                  paddingBottom: '12px',
                  paddingLeft: '10px',
                  borderWidth: '1px'
                }}
              >
                Next
              </button>
            </>
          ) : currentStep === 4 ? (
            <button
              onClick={() => setCurrentStep(5)}
              className="bg-primary-500 text-sm font-medium text-white hover:bg-primary-400 transition-colors border-0"
              style={{
                width: '164px',
                height: '44px',
                opacity: 1,
                borderRadius: '5px',
                paddingTop: '12px',
                paddingRight: '10px',
                paddingBottom: '12px',
                paddingLeft: '10px',
                borderWidth: '1px'
              }}
            >
              Complete Booking
            </button>
          ) : (
            <>
              <button
                onClick={() => {
                  // Print receipt functionality
                  window.print();
                }}
                className="border border-greyscale-200 text-sm font-medium text-woodsmoke-700 hover:bg-greyscale-50 transition-colors"
                style={{
                  width: '164px',
                  height: '44px',
                  opacity: 1,
                  borderRadius: '5px',
                  paddingTop: '12px',
                  paddingRight: '10px',
                  paddingBottom: '12px',
                  paddingLeft: '10px',
                  borderWidth: '1px'
                }}
              >
                Print Receipt
              </button>
              <button
                onClick={() => {
                  // Reset to step 1 for new shipment
                  setCurrentStep(1);
                  // Reset all form data
                  setPickupAddress('');
                  setDeliveryAddress('');
                  setSenderNumber('');
                  setReceiverNumber('');
                  setPackageCategory('');
                  setItem('');
                  setPackageWeight('');
                  setPackageValue('');
                  setQuantity('');
                  setLength('');
                  setWidth('');
                  setHeight('');
                  setPackageDescription('');
                  setSelectedService('express');
                  setAddInsurance(false);
                  setSignatureRequired(false);
                }}
                className="bg-primary-500 text-sm font-medium text-white hover:bg-primary-400 transition-colors border-0"
                style={{
                  width: '186px',
                  height: '44px',
                  opacity: 1,
                  borderRadius: '8px',
                  gap: '4px',
                  paddingTop: '12px',
                  paddingRight: '10px',
                  paddingBottom: '12px',
                  paddingLeft: '10px'
                }}
              >
                Create a new Shipment
              </button>
            </>
          )}
        </div>
      </div>


    </div>
  );
};

export default CreateShipmentModal;
