"use client";
import React, { useState } from 'react';
import { X, Calendar as CalendarIcon, ChevronDownIcon} from 'lucide-react';
import Map from '@/components/Map';
import Image from 'next/image';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import PackageDetailsModal from './PackageDetailsModal';

interface CreateShipmentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateShipmentModal: React.FC<CreateShipmentModalProps> = ({ isOpen, onClose }) => {
  const [pickupAddress, setPickupAddress] = useState('');
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [pickupDate, setPickupDate] = useState<Date | undefined>(undefined);
  const [preferredTime, setPreferredTime] = useState('');
  const [senderNumber, setSenderNumber] = useState('');
  const [receiverNumber, setReceiverNumber] = useState('');
  const [showPackageDetails, setShowPackageDetails] = useState(false);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      <div
        className="bg-white rounded-[8px] relative overflow-hidden shadow-xl pointer-events-auto w-full max-w-[95vw] lg:max-w-[930px] flex flex-col mb-3"
        style={{
          height: '90vh',
          maxHeight: '900px',
          opacity: 1,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          position: 'absolute',
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05)'
        }}
      >
        {/* Header */}
        <div
          className="flex items-center justify-between border-b border-greyscale-100"
          style={{
            width: '930px',
            height: '74px',
            opacity: 1,
            gap: '20px',
            borderBottomWidth: '1px',
            paddingTop: '20px',
            paddingRight: '24px',
            paddingBottom: '20px',
            paddingLeft: '24px',
            borderTopRightRadius: '8px'
          }}
        >
          <h2 className="text-medium text-[18px] font-semibold text-woodsmoke-950">Create your shipment</h2>
          <button
            onClick={onClose}
            className="w-6 h-6 flex items-center justify-center text-woodsmoke-600 hover:text-woodsmoke-800"
          >
            <X size={20} />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-greyscale-100">
          <div className="flex items-center space-x-8">
            {/* Step 1 - Active */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">1</span>
              </div>
              <span className="text-primary-500 text-[12px] text-tiny font-medium">Shipment Services</span>
            </div>

            {/* Connector */}
            <div className="flex-1 h-px bg-greyscale-200"></div>

            {/* Step 2 */}
            <div
              className="flex items-center space-x-2 cursor-pointer"
              onClick={() => setShowPackageDetails(true)}
            >
              <div className="w-8 h-8 bg-greyscale-200 rounded-full flex items-center justify-center">
                <span className="text-greyscale-500 text-sm font-medium">2</span>
              </div>
              <span className="text-greyscale-500 text-[12px] text-tiny font-medium">Package Details</span>
            </div>

            {/* Connector */}
            <div className="flex-1 h-px bg-greyscale-200"></div>

            {/* Step 3 */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-greyscale-200 rounded-full flex items-center justify-center">
                <span className="text-greyscale-500 text-sm font-medium">3</span>
              </div>
              <span className="text-greyscale-500 text-sm font-medium">Shipping Service</span>
            </div>

            {/* Connector */}
            <div className="flex-1 h-px bg-greyscale-200"></div>

            {/* Step 4 */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-greyscale-200 rounded-full flex items-center justify-center">
                <span className="text-greyscale-500 text-sm font-medium">4</span>
              </div>
              <span className="text-greyscale-500 text-sm font-medium">Confirmation</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div
          className="flex-1 border-b border-greyscale-100 overflow-y-auto"
          style={{
            width: '930px',
            height: '598px',
            opacity: 1,
            gap: '16px',
            borderBottomWidth: '1px',
            paddingTop: '20px',
            paddingRight: '24px',
            paddingBottom: '20px',
            paddingLeft: '24px'
          }}
        >
          {/* Address Section */}
          <div className="mb-6">
            <div className="flex items-center space-x-4 mb-4">
              <Image
                src="/icons/PaperPlaneTilt.svg"
                alt="Paper Plane"
                width={20}
                height={20}
                className="text-greyscale-600"
              />
              <div className="flex-1 flex items-center space-x-4">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Pickup Address"
                    value={pickupAddress}
                    onChange={(e) => setPickupAddress(e.target.value)}
                    className="border border-greyscale-200 text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    style={{
                      width: '337px',
                      height: '44px',
                      opacity: 1,
                      borderRadius: '12px',
                      gap: '12px',
                      borderWidth: '1px',
                      paddingTop: '11px',
                      paddingRight: '40px',
                      paddingBottom: '11px',
                      paddingLeft: '16px'
                    }}
                  />
                  {pickupAddress && (
                    <X
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-greyscale-500 cursor-pointer hover:text-greyscale-700"
                      onClick={() => setPickupAddress('')}
                    />
                  )}
                </div>

                {/* Line Connector */}
                <div className="flex items-center justify-center px-4">
                  <Image
                    src="/images/Line.png"
                    alt="Line connector"
                    width={64}
                    height={2}
                  />
                </div>

                <Image
                  src="/icons/MapPin.svg"
                  alt="Map Pin"
                  width={20}
                  height={20}
                  className="text-greyscale-600"
                />
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Delivery Address"
                    value={deliveryAddress}
                    onChange={(e) => setDeliveryAddress(e.target.value)}
                    className="border border-greyscale-200 text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    style={{
                      width: '337px',
                      height: '44px',
                      opacity: 1,
                      borderRadius: '12px',
                      gap: '12px',
                      borderWidth: '1px',
                      paddingTop: '11px',
                      paddingRight: '40px',
                      paddingBottom: '11px',
                      paddingLeft: '16px'
                    }}
                  />
                  {deliveryAddress && (
                    <X
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-greyscale-500 cursor-pointer hover:text-greyscale-700"
                      onClick={() => setDeliveryAddress('')}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Map Section */}
          <div className="mb-6">
            <div className="w-full h-[307px] bg-greyscale-100 rounded-[12px] relative overflow-hidden">
              <Map />
              {/* Zoom Controls */}
              <div className="absolute bottom-4 right-4 flex flex-col space-y-2 z-10">
                <button className="w-10 h-10 bg-white rounded-[8px] shadow-sm flex items-center justify-center text-woodsmoke-800 hover:bg-greyscale-50">
                  +
                </button>
                <button className="w-10 h-10 bg-white rounded-[8px] shadow-sm flex items-center justify-center text-woodsmoke-800 hover:bg-greyscale-50">
                  −
                </button>
              </div>

            </div>
          </div>

          {/* Form Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mb-6 md:mb-8">
            {/* Pickup Date */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">Pickup Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between text-left font-normal px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm hover:bg-transparent focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <span>{pickupDate ? pickupDate.toLocaleDateString() : "Select date"}</span>
                    <CalendarIcon className="h-4 w-4 text-greyscale-500" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={pickupDate}
                    onSelect={setPickupDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Preferred Time */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">Preferred Time</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between text-left font-normal px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm hover:bg-transparent focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <span className="text-greyscale-500">
                      {preferredTime === 'morning' && 'Morning (8AM - 12PM)'}
                      {preferredTime === 'afternoon' && 'Afternoon (12PM - 5PM)'}
                      {preferredTime === 'evening' && 'Evening (5PM - 8PM)'}
                      {!preferredTime && 'Select your preferred time'}
                    </span>
                    <ChevronDownIcon className="h-4 w-4 text-greyscale-500" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <div className="flex flex-col">
                    <button
                      onClick={() => setPreferredTime('morning')}
                      className="px-4 py-3 text-left text-sm hover:bg-greyscale-50 focus:bg-greyscale-50 focus:outline-none"
                    >
                      Morning (8AM - 12PM)
                    </button>
                    <button
                      onClick={() => setPreferredTime('afternoon')}
                      className="px-4 py-3 text-left text-sm hover:bg-greyscale-50 focus:bg-greyscale-50 focus:outline-none"
                    >
                      Afternoon (12PM - 5PM)
                    </button>
                    <button
                      onClick={() => setPreferredTime('evening')}
                      className="px-4 py-3 text-left text-sm hover:bg-greyscale-50 focus:bg-greyscale-50 focus:outline-none"
                    >
                      Evening (5PM - 8PM)
                    </button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Sender Number */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">Sender Number</label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Sender Number"
                  value={senderNumber}
                  onChange={(e) => setSenderNumber(e.target.value)}
                  className="w-full px-4 py-3 pr-10 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                {senderNumber && (
                  <X
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-greyscale-500 cursor-pointer hover:text-greyscale-700"
                    onClick={() => setSenderNumber('')}
                  />
                )}
              </div>
            </div>

            {/* Receiver Number */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">Receiver Number</label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Receiver Number"
                  value={receiverNumber}
                  onChange={(e) => setReceiverNumber(e.target.value)}
                  className="w-full px-4 py-3 pr-10 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                {receiverNumber && (
                  <X
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-greyscale-500 cursor-pointer hover:text-greyscale-700"
                    onClick={() => setReceiverNumber('')}
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-greyscale-100 flex justify-end" style={{ gap: '4px' }}>
          <button
            onClick={onClose}
            className="border border-greyscale-300 text-sm font-medium text-woodsmoke-700 hover:bg-greyscale-50 transition-colors"
            style={{
              width: '164px',
              height: '44px',
              opacity: 1,
              borderRadius: '5px',
              paddingTop: '12px',
              paddingRight: '10px',
              paddingBottom: '12px',
              paddingLeft: '10px',
              borderWidth: '1px'
            }}
          >
            Cancel
          </button>
          <button
            className="bg-primary-500 text-sm font-medium text-white hover:bg-primary-400 transition-colors border-0"
            style={{
              width: '164px',
              height: '44px',
              opacity: 1,
              borderRadius: '5px',
              paddingTop: '12px',
              paddingRight: '10px',
              paddingBottom: '12px',
              paddingLeft: '10px',
              borderWidth: '1px'
            }}
            onClick={() => setShowPackageDetails(true)}
          >
            Next
          </button>
        </div>
      </div>

      {/* Package Details Modal */}
      {showPackageDetails && (
        <div>
          <PackageDetailsModal
            isOpen={showPackageDetails}
            onClose={() => setShowPackageDetails(false)}
            onNext={() => {
              setShowPackageDetails(false);
              // Handle next step logic here
            }}
            onPrevious={() => {
              setShowPackageDetails(false);
              // Return to shipment details
            }}
          />
        </div>
      )}
    </div>
  );
};

export default CreateShipmentModal;
