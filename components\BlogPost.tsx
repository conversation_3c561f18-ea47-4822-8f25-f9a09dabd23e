'use client'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface BlogPostProps {
  title: string
  excerpt: string
  author: {
    name: string
    avatar?: string
  }
  heroImage: {
    src: string
    alt: string
  }
  introduction: string
  sections: {
    title: string
    content: string
  }[]
}

const BlogPost: React.FC<BlogPostProps> = ({
  title,
  excerpt,
  author,
  heroImage,
  introduction,
  sections
}) => {
  return (
    <div className="min-h-screen bg-white pt-14">
      {/* Back to Blog */}
      <div className="py-8 md:py-8 pb-4">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <Link
            href="/blog"
            className="flex items-center gap-2 text-Woodsmoke-950 hover:text-[#171717] transition-colors text-sm font-medium"
          >
            <Image
              src="/icons/Backarrow.svg"
              alt="Back Arrow"
              width={16}
              height={16}
              className="w-4 h-4"
            />
            Back to Blog
          </Link>
        </div>
      </div>

      {/* Hero Section */}
      <div className="pb-6 md:pb-8">
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="grid lg:grid-cols-2 gap-6 md:gap-8 items-start">
            {/* Content - Left side */}
            <div className="space-y-4">
              <h1 className="h3-semibold font-semibold text-[40px] md:text-[36px] lg:text-[42px] leading-[120%] text-Woodsmoke-950">
                {title}
              </h1>

              <p className=" font-regular text-[16px] md:text-[18px] leading-[150%] text-[#454545]">
                {excerpt}
              </p>

              {/* Author Info */}
              <div className="flex items-center gap-3 pt-4">
                <div className="w-10 h-10 rounded-full flex items-center justify-center">
                  {author.avatar ? (
                    <Image
                      src={author.avatar}
                      alt={author.name}
                      width={40}
                      height={40}
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <span className="">
                      {author.name.charAt(0)}
                    </span>
                  )}
                </div>
                <div>
                  <p className="text-woodsmoke-800 text-[14px] text-small font-regular">Written by</p>
                  <p className="text-regular font-semibold text-woodsmoke-950 text-[16px]">{author.name}</p>
                </div>
              </div>
            </div>

            {/* Image - Right side */}
            <div className="relative lg:-mt-16">
              <div className="w-full h-[250px] sm:h-[300px] md:h-[400px] rounded-[12px] md:rounded-[16px] overflow-hidden lg:w-[576px] lg:h-[600px] lg:rounded-[30px] lg:overflow-visible lg:opacity-100">
                <Image
                  src={heroImage.src}
                  alt={heroImage.alt}
                  width={576}
                  height={600}
                  className="w-full h-full object-cover lg:object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
 
      {/* Content Sections */}
      <div className="pb-8 md:pb-12">
        {/* Horizontal rule */}
        <div className="w-full flex justify-center py-[10px] px-[30px] mb-6 md:mb-8">
          <div className="w-[1000px] max-w-full h-px bg-woodsmoke-100 opacity-100"></div>
        </div>

        <div className="max-w-4xl mx-auto px-4 md:px-6">
          <div className="space-y-6 md:space-y-8">
            {/* Introduction */}
            <div className="space-y-4">
              <p className=" font-medium text-large  md:text-[18px] leading-[140%] text-Woodsmoke-800">
                {introduction}
              </p>
            </div>

            {/* All Sections */}
            {sections.map((section, index) => (
              <div key={index} className="space-y-4">
                <h2 className="h4-semibold  text-[32px] md:text-[28px] leading-[130%] text-woodsmoke-950">
                  {section.title}
                </h2>
                <p className=" font-regular text-medium text-[18px] md:text-[18px] leading-[150%] text-Woodsmoke/800">
                  {section.content}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default BlogPost