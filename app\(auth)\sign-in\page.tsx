
import Image from 'next/image'
import React from 'react'

const SignIn = () => {
  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
       {/* MOBILE LOGO BAR */}
      <div className="flex lg:hidden border-b border-woodsmoke-200 px-6 py-4 justify-center items-center">
        <Image
          alt="KOOL LOGISTICS logo"
          src="/icons/site-logo.svg"
          width={118}
          height={56}
          className="object-contain"
        />
      </div>
      <div className="lg:w-1/2 bg-primary-500 hidden lg:flex flex-col justify-between p-8">
        <div>
          <Image alt="KOOL LOGISTICS white logo with arrow" className="w-36 mb-10" height={50} src="/icons/login-logo.svg" width={150}/>
        </div>
        <div className="flex-center">
          <Image alt="Illustration of a man" className="max-w-full h-auto" height={300} src="/icons/login-illustration.svg" width={450}/>
        </div>
        <div className="text-center text-white mt-3">
          <h2 className="h5-semibold mb-3">
            Your Logistics Dashboard
          </h2>
          <p className="text-small text-woodsmoke-50 opacity-70">
            Everything you need easily to Order, Track, and Manage your Packages
          </p>
        </div>
      </div>
      <div className="lg:w-1/2 p-5 py-8 lg:p-10 lg:py-20 flex flex-col justify-start">
        <div className="max-w-[572px] w-full mx-auto">
          <h1 className="h6-semibold lg:h5-semibold mb-3 text-center lg:text-left">
            Sign in to your account
          </h1>
          <p className="text-small font-normal text-woodsmoke-600 mb-8 text-center lg:text-left">
            Welcome back! please enter your detail
          </p>
          <form>
            <div className="mb-6">
              <label className="block text-small font-semibold mb-2">
                Email Address
              </label>
              <input className="w-full border border-woodsmoke-100 rounded-[12px] px-4 py-3 text-woodsmoke-950 text-regular font-semibold placeholder-woodsmoke-500 focus:outline-none focus:ring-2 focus:ring-primary-400" id="email" placeholder="Enter your email address" type="email"/>
            </div>
            <div className="mb-6 relative">
              <label className="block text-small font-semibold mb-2">
                Password
              </label>
              <input className="w-full border border-woodsmoke-100 rounded-[12px] px-4 py-3 pr-12 text-woodsmoke-950 text-regular font-semibold placeholder-woodsmoke-500 focus:outline-none focus:ring-2 focus:ring-primary-400" id="password" placeholder="Enter your password" type="password"/>
              <span className="absolute right-4 top-[69%] -translate-y-1/2 text-woodsmoke-400 cursor-pointer">
                <Image src = "/icons/eye-off.svg" alt = "" width={20} height={18}/>
              </span>
            </div>
            <div className="flex-between mb-8 text-small">
              <label className="flex items-center space-x-2 text-woodsmoke-700">
                {/* Hidden real checkbox */}
                <input
                  type="checkbox"
                  defaultChecked
                  id="remember"
                  className="sr-only peer"
                />
                {/* Custom styled checkbox */}
                <label
                  htmlFor="remember"
                  className="w-4 h-4 rounded-[4px] border border-woodsmoke-300 peer-checked:bg-primary-500 peer-checked:border-primary-500 relative flex-center cursor-pointer transition-all duration-150"
                >
                  <Image src = "/icons/check-white.svg" alt = "" width={8} height={5.5} className= {`mx-auto my-auto`}/>
                </label>
                {/* Label text */}
                <span className="text-small font-medium cursor-pointer">
                  Remember me
                </span>
              </label>
              <a className="text-primary-400 font-semibold hover:underline" href="/forgot-password">
                Forgot Password?
              </a>
            </div>
            <button className="btn-primary w-full" type="submit">
              Sign In
            </button>
            <p className="text-center text-small font-normal mt-8 text-woodsmoke-950">
              Dont have an account?{" "}
              <a className="text-primary-500 font-medium hover:underline" href="/sign-up">
                Sign Up
              </a>
            </p>
          </form>
        </div>
      </div>
    </div>
  )
}

export default SignIn