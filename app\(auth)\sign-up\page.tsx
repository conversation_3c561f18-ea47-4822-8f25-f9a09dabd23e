'use client';

import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';

const SignUp = () => {

  const [step, setStep] = useState(1);

  // Form state, you can expand as needed
  const [accountType, setAccountType] = useState('individual');
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [termsChecked, setTermsChecked] = useState(false);

  const [companyName, setCompanyName] = useState('');
  const [businessType, setBusinessType] = useState('');
  const [companySize, setCompanySize] = useState('');

  const [otp, setOtp] = useState(['', '', '', '']); // array for 4 digits

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showConfirmationAnimated, setShowConfirmationAnimated] = useState(false);

  // Handlers to go next/prev
  const nextStep = () => setStep((s) => Math.min(s + 1, 3));
  const prevStep = () => setStep((s) => Math.max(s - 1, 1));

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>, idx: number) => {
    const val = e.target.value;
    if (/^\d?$/.test(val)) { // allow only single digits or empty
      const newOtp = [...otp];
      newOtp[idx] = val;
      setOtp(newOtp);
      // Auto-focus next input if digit entered
      if (val && idx < 3) {
        const nextInput = document.getElementById(`otp-${idx + 1}`) as HTMLInputElement | null;
        if (nextInput) nextInput.focus();
      }
    }
  };

  // Step indicator dots
  const StepIndicator = () => (
    <div className="flex space-x-2 mb-8 mt-4 justify-center">
      {[1, 2, 3].map((num) => (
        <span
          key={num}
          className={`w-3 h-3 rounded-full block ${
            step === num ? 'bg-primary-500' : 'bg-primary-200 opacity-20'
          }`}
          aria-label={`Step ${num}`}
        />
      ))}
    </div>
  );

 
  const options = [
    { label: 'Individual', value: 'individual' },
    { label: 'Admin', value: 'admin' },
  ];

  const CustomDropdown = ({
    placeholder,
    options,
    value,
    onChange,
  }: {
    placeholder: string;
    options: { label: string; value: string }[];
    value: string;
    onChange: (val: string) => void;
  }) => {
    const [open, setOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const handleClickOutside = (e: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
          setOpen(false);
        }
      };
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const selectedOption = options.find((o) => o.value === value);

    return (
      <div className="relative mb-6" ref={dropdownRef}>
        <div
          onClick={() => setOpen(!open)}
          className="w-full cursor-pointer border border-woodsmoke-100 rounded-[12px] px-4 py-3 flex justify-between items-center text-regular font-semibold text-woodsmoke-950 placeholder-woodsmoke-500 focus:outline-none focus:ring-2 focus:ring-primary-400"
        >
          <span className={`${!value ? 'text-woodsmoke-400' : ''}`}>
            {selectedOption?.label || placeholder}
          </span>
          <Image
            src="/icons/CaretDown.svg"
            alt=""
            width={20}
            height={20}
            className={`transition-transform duration-200 ${open ? 'rotate-180' : ''}`}
          />
        </div>

        {open && (
          <ul className="absolute z-10 mt-2 w-full border border-woodsmoke-100 bg-white rounded-[12px] max-h-60 overflow-auto" style={{ boxShadow: '0px -2px 16px 0px #00000014' }}>
            {options.map((opt) => (
              <li
                key={opt.value}
                onClick={() => {
                  onChange(opt.value);
                  setOpen(false);
                }}
                className={`px-4 py-3 text-woodsmoke-950 text-large font-[400] hover:bg-primary-50 cursor-pointer ${
                  opt.value === value ? 'bg-primary-100' : ''
                }`}
              >
                {opt.label}
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-white">
      {/* MOBILE LOGO BAR */}
      <div className="flex lg:hidden border-b border-woodsmoke-200 px-6 py-4 justify-center items-center">
        <Image
          alt="KOOL LOGISTICS logo"
          src="/icons/site-logo.svg"
          width={118}
          height={56}
          className="object-contain"
        />
      </div>
      {/* Left panel */}
      <div className="lg:w-1/2 bg-primary-500 hidden lg:flex flex-col justify-between p-8 text-white">
        <div>
          <Image
            alt="KOOL LOGISTICS white logo"
            className="w-36 mb-10"
            height={50}
            src="/icons/login-logo.svg"
            width={150}
          />
        </div>
        <div className="flex justify-center">
          <Image
            alt="Illustration of a man"
            className="max-w-full h-auto"
            height={300}
            src="/icons/login-illustration.svg"
            width={450}
          />
        </div>
        <div className="text-center mt-3">
          <h2 className="h5-semibold mb-3">Your Logistics Dashboard</h2>
          <p className="text-small opacity-70">
            Everything you need easily to Order, Track, and Manage your Packages
          </p>
        </div>
      </div>

      {/* Right panel: forms */}
      <div className="lg:w-1/2 p-5 py-8 md:p-10 md:py-20 flex flex-col justify-start">
        <div className="max-w-[572px] w-full mx-auto">
          {step === 1 && (
            <>
              <h1 className="h6-semibold lg:h5-semibold mb-3 text-center lg:text-left">Create your account</h1>
              <p className="text-small font-normal text-woodsmoke-600 mb-8 text-center lg:text-left">Enter your details to get started</p>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  nextStep();
                }}
              >
                <fieldset className="flex-center space-x-6 mb-8 justify-center">
                  {options.map((option) => (
                    <label key={option.value} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="radio"
                        name="accountType"
                        value={option.value}
                        checked={accountType === option.value}
                        onChange={() => setAccountType(option.value as 'individual' | 'admin')}
                        className="sr-only"
                      />
                      <span
                        className={`w-4 h-4 flex items-center justify-center rounded-full border 
                          ${accountType === option.value
                            ? 'bg-primary-500 border-primary-500 text-white'
                            : 'border-woodsmoke-200 text-transparent'}
                        `}
                      >
                        <Image src = "/icons/check-white.svg" alt = "" width={8} height={5.5} className= {`mx-auto my-auto ${accountType === option.value ? 'block' : 'hidden'}`}/>
                      </span>
                      <span className="text-small font-medium text-woodsmoke-950">
                        {option.label}
                      </span>
                    </label>
                  ))}
                </fieldset>

                <div className="mb-4">
                  <label htmlFor="fullName" className="block text-small font-semibold mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Enter your full name"
                    className="w-full border border-woodsmoke-100 rounded-[12px] px-4 py-3 text-woodsmoke-950 text-regular font-semibold placeholder-woodsmoke-500 focus:outline-none focus:ring-2 focus:ring-primary-400"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="email" className="block text-small font-semibold mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className="w-full border border-woodsmoke-100 rounded-[12px] px-4 py-3 text-woodsmoke-950 text-regular font-semibold placeholder-woodsmoke-500 focus:outline-none focus:ring-2 focus:ring-primary-400"
                  />
                </div>

                <div className="mb-4 relative">
                  <label htmlFor="password" className="block text-small font-semibold mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="w-full border border-woodsmoke-100 rounded-[12px] px-4 py-3 pr-12 text-woodsmoke-950 text-regular font-semibold placeholder-woodsmoke-500 focus:outline-none focus:ring-2 focus:ring-primary-400"
                  />
                  <span className="absolute right-4 top-[69%] -translate-y-1/2 text-woodsmoke-400 cursor-pointer">
                    {/* You can add toggle password visibility here */}
                    <Image src = "/icons/eye-off.svg" alt = "" width={20} height={18}/>
                  </span>
                </div>

                <div className="flex items-start mb-8">
                  {/* Visually hidden actual checkbox for accessibility */}
                  <input
                    type="checkbox"
                    id="terms"
                    checked={termsChecked}
                    onChange={() => setTermsChecked(prev => !prev)}
                    className="sr-only peer"
                  />

                  {/* Custom checkbox */}
                  <label
                    htmlFor="terms"
                    className="w-4 h-4 mt-1 rounded-[4px] border border-woodsmoke-300 peer-checked:bg-primary-500 peer-checked:border-primary-500 relative flex items-center justify-center cursor-pointer transition-all duration-150"
                  >
                    <Image src = "/icons/check-white.svg" alt = "" width={8} height={5.5} className= {`mx-auto my-auto ${termsChecked ? 'block' : 'hidden'}`}/>
                  </label>

                  {/* Label text */}
                  <label
                    htmlFor="terms"
                    className="ml-2 text-extrasmall text-woodsmoke-500 leading-5 cursor-pointer font-inter"
                  >
                    By creating an account means you agree to the
                    <strong className="text-woodsmoke-950">&nbsp;Terms <br />&amp; Conditions&nbsp;</strong> and our
                    <strong className="text-woodsmoke-950">&nbsp;Privacy Policy</strong>
                  </label>
                </div>

                <div className="flex-between">
                  <StepIndicator />
                  <button
                    type="submit"
                    className="btn-primary w-[122px] hover:bg-primary-400 h-14"
                  >
                    Next
                  </button>
                </div>
              </form>
              <p className="text-center text-small font-medium text-woodsmoke-950 mt-10">
                Already have an account?&nbsp;
                <a className="text-primary-500 font-semibold hover:underline" href="/sign-in">
                  Log In
                </a>
              </p>
            </>
          )}

          {step === 2 && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                nextStep();
              }}
            >
              <h3 className="h6-semibold lg:h5-semibold mb-3 text-center lg:text-left">Company Information</h3>
              <p className="text-small font-normal text-woodsmoke-600 mb-8 text-center lg:text-left">Tell us about your business</p>

              <label htmlFor="companyName" className="block text-small font-semibold text-woodsmoke-950 mb-2">
                Company Name
              </label>
              <input
                type="text"
                id="companyName"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                placeholder="Enter Company Name"
                className="w-full border border-woodsmoke-100 rounded-[12px] px-4 py-3 mb-6 text-regular font-semibold text-woodsmoke-950 placeholder-woodsmoke-500 focus:outline-none focus:ring-2 focus:ring-primary-400"
              />

              <label htmlFor="businessType" className="block text-small font-semibold text-woodsmoke-950 mb-2">
                Business Type
              </label>
              <CustomDropdown
                placeholder="Select your business type"
                value={businessType}
                onChange={setBusinessType}
                options={[
                  { label: 'Type 1', value: 'type1' },
                  { label: 'Type 2', value: 'type2' },
                  { label: 'Type 3', value: 'type3' },
                  { label: 'Type 4', value: 'type4' },
                ]}
              />


              <label htmlFor="companySize" className="block text-small font-semibold text-woodsmoke-950 mb-2">
                Company Size
              </label>
              <CustomDropdown
                placeholder="Select company size"
                value={companySize}
                onChange={setCompanySize}
                options={[
                  { label: '0 - 10 persons', value: '1-10' },
                  { label: '11 - 50 persons', value: '11-50' },
                  { label: '51 - 100 persons', value: '51-200' },
                  { label: '300 - 1000 persons', value: '300-1000' },
                ]}
              />

              <div className="flex-between lg:flex-row flex-col">
              <StepIndicator />
              <div className="flex-between space-x-4">
                <button
                  type="button"
                  onClick={prevStep}
                  className="btn-secondary lg:w-[122px] w-[167px] hover:bg-woodsmoke-200 transition h-14"
                >
                  Previous
                </button>
                <button
                  type="submit"
                  className="btn-primary lg:w-[122px] w-[167px] hover:bg-primary-400 transition h-14"
                >
                  Next
                </button>
              </div>
              </div>

              <p className="text-center text-small font-medium text-woodsmoke-950 mt-10">
                Already have an account?&nbsp;
                <a className="text-primary-500 font-semibold hover:underline" href="/sign-in">
                  Log In
                </a>
              </p>
            </form>
          )}

          {step === 3 && (
            <>
              <h3 className="h6-semibold lg:h5-semibold mb-3 text-center lg:text-left">Verification</h3>
              <p className="text-small font-normal text-woodsmoke-600 mb-8 text-center lg:text-left">Verify your email to continue</p>

              <div className="flex justify-center mb-10">
                <div className="bg-primary-50 rounded-full p-5 w-[80px] h-[80px] flex items-center justify-center">
                  <Image src = "/icons/Email.svg" alt = "" width={40} height={40}/>
                </div>
              </div>

              <p className="text-center text-regular font-medium text-woodsmoke-600 mb-5">
                We’ve sent a verification code to your email
              </p>

              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  if (otp.some((digit) => digit === '')) {
                    alert('Please enter the full OTP code');
                    return;
                  }
                  alert('Verification complete!');
                  // Add further logic here
                }}
                className="flex justify-center space-x-4 mb-4"
              >
                {otp.map((digit, idx) => (
                  <input
                    key={idx}
                    id={`otp-${idx}`}
                    type="text"
                    maxLength={1}
                    inputMode="numeric"
                    pattern="[0-9]*"
                    value={digit}
                    onChange={(e) => handleOtpChange(e, idx)}
                    className="w-14 h-14 border border-woodsmoke-100 rounded-[12px] text-center text-regular font-normal focus:outline-none focus:ring-2 focus:ring-primary-400"
                    aria-label={`Verification code digit ${idx + 1}`}
                  />
                ))}
              </form>

              <p className="text-center text-small font-medium text-woodsmoke-500 mb-12">
                Didn’t receive the code?{' '}
                <button
                  type="button"
                  className="text-primary-500 font-bold hover:underline focus:outline-none"
                  onClick={() => alert('Code resent!')}
                >
                  Resend
                </button>
              </p>


              <div className="flex-between lg:flex-row flex-col">
                <StepIndicator />
                <div className="flex-between space-x-4">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="btn-secondary lg:w-[122px] w-[167px] h-14 hover:bg-woodsmoke-200"
                  >
                    Previous
                  </button>
                  <button
                    type="submit"
                    className="btn-primary lg:w-[122px] w-[167px] h-14 hover:bg-primary-400"
                    onClick={(e) => {
                      e.preventDefault();
                      setShowConfirmation(true);
                      setTimeout(() => setShowConfirmationAnimated(true), 50); // triggers fade-in
                    }}
                  >
                    Complete
                  </button>
                </div>
              </div>

              <p className="text-center text-small font-medium text-woodsmoke-950 mt-10">
                Already have an account?&nbsp;
                <a className="text-primary-500 font-semibold hover:underline" href="/sign-in">
                  Log In
                </a>
              </p>
            </>
          )}
        </div>
      </div>

      {showConfirmation && (
        <div
          className={`fixed inset-0 z-50 bg-white flex-center transition-opacity duration-700 ease-in-out ${
            showConfirmationAnimated ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <main className="flex flex-col items-center space-y-6">
            <Image src="/icons/login-check.svg" alt="check" width={100} height={50} className="w-24 mb-8" />
            <h1 className="text-center h6-semibold lg:h5-semibold text-woodsmoke-950 leading-[140%] mb-3">
              Great Job🎉<br />
              <span className="block text-center">Your email is verified</span>
            </h1>
            <p className="text-center text-small text-woodsmoke-600 leading-tight max-w-xs">
              Use it to log in to your account
            </p>
            <a
              href="/sign-in"
              className="mt-4 btn-primary w-[122px] text-center focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-1"
            >
              Continue
            </a>
          </main>
        </div>
      )}
    </div>
  );
};

export default SignUp;
