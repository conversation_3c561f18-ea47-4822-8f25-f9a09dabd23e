import ROUTES from "@/constants/routes";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const LandingPageContact = () => {
  return (
    <div className="flex flex-col lg:flex-row gap-4 lg:gap-0">
      <div className="hidden lg:block w-full lg:w-4/10 relative h-[300px] sm:h-[400px] lg:h-auto lg:min-h-[400px]">
        <Image
          src="/images/man-carrying-boxes.png"
          alt="delivery-mans"
          fill={true}
          className="object-cover rounded-[20px]"
          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 40vw"
        />
      </div>
      <div className="w-full lg:w-6/10 bg-primary-100 rounded-[20px] py-[40px] sm:py-[60px] lg:py-[76px] px-[24px] sm:px-[40px] lg:px-[56px]">
        <p className="text-small sm:text-medium text-woodsmoke-950">
          HIRE US FOR KOOL DELIVERY
        </p>
        <h2 className="text-[24px] sm:text-[32px] lg:h2-semibold text-woodsmoke-950 max-w-xl mt-[16px] sm:mt-[20px] mb-[20px] sm:mb-[24px] font-semibold leading-tight">
          Looking for the best logistics transport service?
        </h2>
        <div className="flex flex-col sm:flex-row gap-4 sm:gap-0">
          <Link href={ROUTES.QUOTE}>
            <button className="btn-primary w-full sm:w-auto sm:mr-[20px]">
              Request Quote
            </button>
          </Link>
          <div className="flex justify-center sm:justify-start items-center">
            <Image
              src="/icons/PhoneCall.svg"
              alt="call"
              width={15.62473201751709}
              height={15.62473201751709}
              className="mr-[10.5px]"
            />
            <span className="text-regular font-semibold text-woodsmoke-950">
              081 342 167111
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPageContact;
