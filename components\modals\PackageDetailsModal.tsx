"use client";

import React, { useState } from 'react';
import { X, ChevronDownIcon } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

interface PackageDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onNext: () => void;
  onPrevious: () => void;
}

const PackageDetailsModal: React.FC<PackageDetailsModalProps> = ({ 
  isOpen, 
  onClose, 
  onNext, 
  onPrevious 
}) => {
  const [packageCategory, setPackageCategory] = useState('');
  const [item, setItem] = useState('');
  const [packageWeight, setPackageWeight] = useState('');
  const [value, setValue] = useState('');
  const [quantity, setQuantity] = useState('2');
  const [length, setLength] = useState('');
  const [width, setWidth] = useState('');
  const [height, setHeight] = useState('');
  const [packageDescription, setPackageDescription] = useState('');

  const packageCategories = [
    'Electronics',
    'Clothing',
    'Books',
    'Food Items',
    'Fragile Items',
    'Documents'
  ];

  const items = [
    'Laptop',
    'Phone',
    'Tablet',
    'Clothes',
    'Shoes',
    'Books',
    'Documents'
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      <div
        className="bg-white rounded-[8px] relative overflow-hidden shadow-xl pointer-events-auto w-full max-w-[95vw] lg:max-w-[930px] flex flex-col mb-3"
        style={{
          height: '90vh',
          maxHeight: '900px',
          opacity: 1,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          position: 'absolute',
          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05)'
        }}
      >
        {/* Header */}
        <div 
          className="flex items-center justify-between border-b border-greyscale-100"
          style={{
            width: '930px',
            height: '74px',
            opacity: 1,
            gap: '20px',
            borderBottomWidth: '1px',
            paddingTop: '20px',
            paddingRight: '24px',
            paddingBottom: '20px',
            paddingLeft: '24px',
            borderTopRightRadius: '8px'
          }}
        >
          <h2 className="text-medium text-[18px] font-semibold text-woodsmoke-950">Create your shipment</h2>
          <button 
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center text-greyscale-400 hover:text-woodsmoke-950 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-greyscale-100">
          <div className="flex items-center space-x-8 overflow-x-auto">
            {/* Step 1 - Completed */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-greyscale-200 rounded-full flex items-center justify-center">
                <span className="text-greyscale-500 text-sm font-medium">1</span>
              </div>
              <span className="text-greyscale-500 text-[12px] text-tiny font-medium">Shipment Details</span>
            </div>

            {/* Connector */}
            <div className="flex-1 h-px bg-greyscale-200"></div>

            {/* Step 2 - Active */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">2</span>
              </div>
              <span className="text-primary-500 text-[12px] text-tiny font-medium">Package Details</span>
            </div>

            {/* Connector */}
            <div className="flex-1 h-px bg-greyscale-200"></div>

            {/* Step 3 */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-greyscale-200 rounded-full flex items-center justify-center">
                <span className="text-greyscale-500 text-sm font-medium">3</span>
              </div>
              <span className="text-greyscale-500 text-[12px] text-tiny font-medium">Shipping Service</span>
            </div>

            {/* Connector */}
            <div className="flex-1 h-px bg-greyscale-200"></div>

            {/* Step 4 */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-greyscale-200 rounded-full flex items-center justify-center">
                <span className="text-greyscale-500 text-sm font-medium">4</span>
              </div>
              <span className="text-greyscale-500 text-sm font-medium">Confirmation</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div 
          className="flex-1 border-b border-greyscale-100 overflow-y-auto"
          style={{
            width: '930px',
            height: '598px',
            opacity: 1,
            gap: '16px',
            borderBottomWidth: '1px',
            paddingTop: '20px',
            paddingRight: '24px',
            paddingBottom: '20px',
            paddingLeft: '24px'
          }}
        >
          {/* Form Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Package Category */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                Package Category <span className="text-[#DF1C41]">*</span>
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between text-left font-normal px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm hover:bg-transparent focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <span className="text-greyscale-500">
                      {packageCategory || 'Select Category'}
                    </span>
                    <ChevronDownIcon className="h-4 w-4 text-greyscale-500" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <div className="flex flex-col">
                    {packageCategories.map((category) => (
                      <button
                        key={category}
                        onClick={() => setPackageCategory(category)}
                        className="px-4 py-3 text-left text-sm hover:bg-greyscale-50 focus:bg-greyscale-50 focus:outline-none"
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Item */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                Item <span className="text-[#DF1C41]">*</span>
              </label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between text-left font-normal px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm hover:bg-transparent focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <span className="text-greyscale-500">
                      {item || 'Select Item'}
                    </span>
                    <ChevronDownIcon className="h-4 w-4 text-greyscale-500" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0" align="start">
                  <div className="flex flex-col">
                    {items.map((itemOption) => (
                      <button
                        key={itemOption}
                        onClick={() => setItem(itemOption)}
                        className="px-4 py-3 text-left text-sm hover:bg-greyscale-50 focus:bg-greyscale-50 focus:outline-none"
                      >
                        {itemOption}
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Second Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {/* Package Weight */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                Package Weight (KG) <span className="text-[#DF1C41]">*</span>
              </label>
              <input
                type="text"
                placeholder="12.5"
                value={packageWeight}
                onChange={(e) => setPackageWeight(e.target.value)}
                className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            {/* Value */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                Value (Naira) <span className="text-[#DF1C41]">*</span>
              </label>
              <input
                type="text"
                placeholder="10,000.00"
                value={value}
                onChange={(e) => setValue(e.target.value)}
                className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            {/* Quantity */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                Quantity <span className="text-[#DF1C41]">*</span>
              </label>
              <input
                type="text"
                 placeholder="1"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Third Row - Dimensions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {/* Length */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                Length (Optional)
              </label>
              <input
                type="text"
                placeholder="Length (cm)"
                value={length}
                onChange={(e) => setLength(e.target.value)}
                className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            {/* Width */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                Width (Optional)
              </label>
              <input
                type="text"
                placeholder="Width (cm)"
                value={width}
                onChange={(e) => setWidth(e.target.value)}
                className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            {/* Height */}
            <div>
              <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
                Height (Optional)
              </label>
              <input
                type="text"
                placeholder="Height (cm)"
                value={height}
                onChange={(e) => setHeight(e.target.value)}
                className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Package Description */}
          <div className="mb-6">
            <label className="block text-small font-semibold text-[14px] text-woodsmoke-950 mb-2">
              Package Description (Optional)
            </label>
            <textarea
              placeholder="Describe the contents of your package..."
              value={packageDescription}
              onChange={(e) => setPackageDescription(e.target.value)}
              rows={4}
              className="w-full px-4 py-3 border border-greyscale-200 rounded-[8px] text-sm placeholder:text-greyscale-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-greyscale-100 flex justify-end" style={{ gap: '4px' }}>
          <button
            onClick={onPrevious}
            className="border border-greyscale-300 text-sm font-medium text-woodsmoke-700 hover:bg-greyscale-50 transition-colors"
            style={{
              width: '164px',
              height: '44px',
              opacity: 1,
              borderRadius: '5px',
              paddingTop: '12px',
              paddingRight: '10px',
              paddingBottom: '12px',
              paddingLeft: '10px',
              borderWidth: '1px'
            }}
          >
            Previous
          </button>
          <button 
            onClick={onNext}
            className="!bg-primary-500 text-sm font-medium text-white hover:bg-primary-400 transition-colors border-0"
            style={{
              width: '164px',
              height: '44px',
              opacity: 1,
              borderRadius: '5px',
              paddingTop: '12px',
              paddingRight: '10px',
              paddingBottom: '12px',
              paddingLeft: '10px',
              borderWidth: '1px'
            }}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default PackageDetailsModal;
